# JSON to Dart Model Generator

A modern, responsive web application that converts JSON data to Dart model classes with null safety, serialization methods, and customizable options.

🌐 **Live Demo**: [https://ashamp.github.io/jsonToDartModel/](https://ashamp.github.io/jsonToDartModel/)

## ✨ Features

### Core Functionality
- **JSON to Dart Conversion**: Convert JSON objects to well-structured Dart model classes
- **Null Safety Support**: Generate null-safe Dart code compatible with modern Dart/Flutter
- **Serialization Methods**: Automatic generation of `fromJson()` and `toJson()` methods
- **Custom Configuration**: Extensive options for code generation customization

### Modern Web Features
- **Progressive Web App (PWA)**: Install and use offline
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Dark/Light Theme**: Automatic theme detection with manual toggle
- **Touch-Friendly**: Optimized for touch interactions on mobile devices
- **Offline Support**: Service worker enables offline functionality

### Code Generation Options
- **<PERSON> Case to Camel Case**: Convert property names automatically
- **J<PERSON><PERSON> Key Annotations**: Generate `@JsonKey` annotations for json_serializable
- **Fault Tolerance**: Handle mismatched data types gracefully
- **Force String Types**: Convert all properties to String type (except bool)
- **Store Original JSON**: Keep reference to original JSON data
- **Custom Class Names**: Specify root class name and auto-generate file names
- **Dart Keyword Protection**: Automatically handle Dart reserved keywords
- **Multidimensional Arrays**: Support for complex nested array structures

### User Experience
- **Real-time Preview**: See generated code as you type
- **Syntax Highlighting**: Beautiful Dart code highlighting
- **Copy to Clipboard**: One-click code copying
- **Download Files**: Save generated code as .dart files
- **Format JSON**: Auto-format JSON input
- **Error Handling**: Clear error messages and validation
- **Toast Notifications**: User-friendly feedback system

## 🚀 Quick Start

### Online Usage
1. Visit [https://ashamp.github.io/jsonToDartModel/](https://ashamp.github.io/jsonToDartModel/)
2. Paste your JSON data in the left panel
3. Configure generation options as needed
4. Enter a root class name
5. Copy or download the generated Dart code

### PWA Installation
- **Desktop**: Look for the install icon in your browser's address bar
- **Mobile**: Use "Add to Home Screen" from your browser menu
- **Benefits**: Offline access, faster loading, native app experience

## ⚠️ Important Notes
- Objects should have at least one property
- Only the first object in arrays will be parsed for structure
- Empty arrays will cause parsing errors
- When `Force String Type` is selected, `bool` types remain unchanged
- Complex nested structures are fully supported

## 📝 Example

### Input JSON
```json
{
  "user_id": 123,
  "user_name": "John Doe",
  "email": "<EMAIL>",
  "is_active": true,
  "profile": {
    "age": 30,
    "location": "New York"
  },
  "tags": ["developer", "flutter"],
  "scores": [95.5, 87.2, 92.1]
}
```

### Generated Dart Code
```dart
///
/// Code generated by JSON to Dart Model Generator
/// https://ashamp.github.io/jsonToDartModel/
///

class UserModel {
  int? userId;
  String? userName;
  String? email;
  bool? isActive;
  UserModelProfile? profile;
  List<String>? tags;
  List<double>? scores;

  UserModel({
    this.userId,
    this.userName,
    this.email,
    this.isActive,
    this.profile,
    this.tags,
    this.scores,
  });

  UserModel.fromJson(Map<String, dynamic> json) {
    userId = json['user_id']?.toInt();
    userName = json['user_name']?.toString();
    email = json['email']?.toString();
    isActive = json['is_active'];
    profile = (json['profile'] != null) ? UserModelProfile.fromJson(json['profile']) : null;
    if (json['tags'] != null) {
      final v = json['tags'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      tags = arr0;
    }
    if (json['scores'] != null) {
      final v = json['scores'];
      final arr0 = <double>[];
      v.forEach((v) {
        arr0.add(v.toDouble());
      });
      scores = arr0;
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_id'] = userId;
    data['user_name'] = userName;
    data['email'] = email;
    data['is_active'] = isActive;
    if (profile != null) {
      data['profile'] = profile!.toJson();
    }
    if (tags != null) {
      final v = tags;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['tags'] = arr0;
    }
    if (scores != null) {
      final v = scores;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['scores'] = arr0;
    }
    return data;
  }
}

class UserModelProfile {
  int? age;
  String? location;

  UserModelProfile({
    this.age,
    this.location,
  });

  UserModelProfile.fromJson(Map<String, dynamic> json) {
    age = json['age']?.toInt();
    location = json['location']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['age'] = age;
    data['location'] = location;
    return data;
  }
}
```

### Usage in Flutter
```dart
// Parse JSON
final user = UserModel.fromJson(jsonDecode(jsonString));

// Convert back to JSON
final jsonString = jsonEncode(user.toJson());

// Access properties
print('User: ${user.userName}');
print('Email: ${user.email}');
print('Location: ${user.profile?.location}');
```

## 🛠 Technical Details

### Modern Architecture
- **ES6+ JavaScript**: Modular design with classes and modules
- **CSS Custom Properties**: Consistent theming system
- **Service Worker**: Offline support and intelligent caching
- **Responsive Design**: CSS Grid and Flexbox layouts
- **Progressive Enhancement**: Works on all modern browsers

### Browser Support
- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers with ES6 support

## 📱 Progressive Web App Features

- **Offline Functionality**: Works without internet connection
- **Install Prompt**: Native installation experience
- **File Handling**: Open JSON files directly in the app
- **Share Target**: Receive shared JSON data from other apps
- **Background Sync**: Sync data when connection is restored
- **Responsive**: Adapts to any screen size

## 🎨 Customization

### Theme Support
- **Auto Detection**: Respects system preferences
- **Manual Toggle**: Switch between light and dark themes
- **Persistent**: Settings saved locally

### Configuration Options
- JSON Key annotations with private constants
- Snake case to camelCase conversion
- Null safety support
- Fault tolerance for type mismatches
- Force String type conversion
- Original JSON storage

## 🤝 Contributing

Contributions are welcome! Please feel free to:
- Report bugs
- Suggest new features
- Submit pull requests
- Improve documentation

## 📄 License

This project is open source and available under the MIT License.

## 🔗 Links

- **Live Demo**: [https://ashamp.github.io/jsonToDartModel/](https://ashamp.github.io/jsonToDartModel/)
- **GitHub Repository**: [https://github.com/ashamp/jsonToDartModel](https://github.com/ashamp/jsonToDartModel)
- **Issues**: [Report bugs or request features](https://github.com/ashamp/jsonToDartModel/issues)

![JSON to Dart Model Generator](readme.png)
