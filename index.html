<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico?">
    <link rel="stylesheet" href="styles.css">
    <title>Json To Dart Model</title>
</head>

<body>
    <div class="divContainer divContainer mainContent">
        <div class="divContainer header">
            <!-- 标题 -->
            <h1>Json To Dart Model Code Generator</h1>
            <a href="https://github.com/ashamp/jsonToDartModel/tree/gh-pages" target="_blank">view source</a>
        </div>
        <div class="divContainer info">
            <!-- 提示信息 -->

        </div>
        <div class="divContainer mainContainer">
            <!-- 主容器 -->
            <div class="divContainer leftContainer">
                <!-- 左容器 -->
                <div class="divContainer jsonContainer">
                    <!-- json容器 -->
                    <div class="origJsonContainer">
                        <!-- 原始json -->
                        <!-- <textarea name="origJsonTextarea" id="origJsonTextarea"></textarea> -->
                        <div id="origJsonContainer" style="height: 100%"></div>
                    </div>
                </div>
                <div class="divContainer inputsContainer">
                    <!-- Input容器 -->
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>JsonKey:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="usingJsonKeyCheckBox" id="usingJsonKeyCheckBox"> Json key string</p>
                            <p>&nbsp; | &nbsp;</p>
                            <p><input type="checkbox" name="jsonKeyPrivateCheckBox" id="jsonKeyPrivateCheckBox"> Private</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Camel Case:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="camelCheckBox" id="camelCheckBox"> Convert snake case to camel case</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Null safe:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="nullSafeCheckBox" id="nullSafeCheckBox"> Enable null safe</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Fault tolerance:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="faultToleranceCheckBox" id="faultToleranceCheckBox"> Fault tolerance for JSON with mismatched data types</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Force String Type:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="forceStringCheckBox" id="forceStringCheckBox"> Convert all props to String type (Except bool)</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Store Original Json:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <p><input type="checkbox" name="origJsonCheckBox" id="origJsonCheckBox"> Enable store original Json</p>
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>Root Class Name:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <input type="text" name="className" id="classNameTextField" placeholder="Typecally you can using interface name">
                        </div>
                    </div>
                    <div class="divContainer textInputContainer">
                        <div class="divContainer inputTitle">
                            <p>File Name:</p>
                        </div>
                        <div class="divContainer inputContainer">
                            <input type="text" name="className" disabled id="fileNameTextField" placeholder="Auto generated snake case file name">
                        </div>
                    </div>
                </div>
            </div>
            <div class="divContainer rightContainer">
                <!-- 右容器 -->
                <div class="divContainer objcHeader">
                    <!-- 头文件 -->
                    <pre><code id="dartCode" class="objectivec"></code></pre>
                </div>
                <div class="divContainer copyBtnContaner">
                    <button id="copyFileBtn" class="copyBtn">Copy</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.7.0/jsoneditor.min.js" integrity="sha512-T9isxS19cp5TyUWFgUh67kjuTcafB/OyIxav2G1whJda6cmiGidGtqZUl1Iep+Hs6tS8kvl5c+MT4xfWuUUv0w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.7.0/jsoneditor.css" integrity="sha512-+dwBKGKkHTEGq+HPC9veK/RMCoS7iHZPy337aQsm2iiz0nSm5yp6WkbK7+UjlTTlCADz9hliigkfmq9fhf29Wg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.min.js"></script>
    <link rel="stylesheet" href="styles/default.css">
    <script src="highlight.pack.js"></script>
    <script src="code.js"></script>
</body>

</html>
