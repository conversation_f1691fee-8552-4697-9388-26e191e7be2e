<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Convert JSON data to Dart model classes with null safety, serialization methods, and customizable options">
    <meta name="keywords" content="JSON, Dart, Flutter, Model, Code Generator, Serialization, Null Safety">
    <meta name="author" content="JSON to Dart Model Generator">
    <meta name="theme-color" content="#2196F3">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://ashamp.github.io/jsonToDartModel/">
    <meta property="og:title" content="JSON to Dart Model Generator">
    <meta property="og:description" content="Convert JSON data to Dart model classes with null safety, serialization methods, and customizable options">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://ashamp.github.io/jsonToDartModel/">
    <meta property="twitter:title" content="JSON to Dart Model Generator">
    <meta property="twitter:description" content="Convert JSON data to Dart model classes with null safety, serialization methods, and customizable options">

    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="favicon.ico">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="JSON to Dart">

    <!-- Windows PWA Support -->
    <meta name="msapplication-TileColor" content="#2196F3">
    <meta name="msapplication-config" content="browserconfig.xml">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <title>JSON to Dart Model Generator</title>
</head>

<body>
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner" aria-hidden="true">
        <div class="spinner"></div>
        <p>Processing JSON...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container" aria-live="polite"></div>

    <!-- PWA Install Prompt -->
    <div id="pwa-install-banner" class="pwa-install-banner" style="display: none;">
        <div class="pwa-install-content">
            <div class="pwa-install-info">
                <svg class="pwa-install-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-15"></path>
                    <polyline points="7,10 12,15 17,10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                <div class="pwa-install-text">
                    <h3>Install JSON to Dart Generator</h3>
                    <p>Get quick access and work offline</p>
                </div>
            </div>
            <div class="pwa-install-actions">
                <button id="pwa-install-dismiss" class="secondary-button">
                    <span>Not now</span>
                </button>
                <button id="pwa-install-button" class="primary-button">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-15"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <span>Install</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>JSON to Dart Model Generator</h1>
                    <p class="header-subtitle">Convert JSON data to Dart model classes with null safety and serialization</p>
                </div>
                <div class="header-actions">
                    <button id="theme-toggle" class="icon-button" aria-label="Toggle dark mode" title="Toggle dark mode">
                        <svg class="theme-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                    </button>
                    <a href="https://github.com/ashamp/jsonToDartModel/tree/gh-pages" target="_blank" rel="noopener noreferrer" class="github-link" aria-label="View source code on GitHub">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                        <span>View Source</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Alert/Info Banner -->
        <div id="info-banner" class="info-banner" role="alert" aria-live="polite" style="display: none;">
            <div class="info-content">
                <svg class="info-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <span id="info-message"></span>
                <button id="info-close" class="info-close" aria-label="Close notification">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        </div>
        <!-- Main Content -->
        <main class="main-content">
            <div class="content-grid">
                <!-- JSON Input Section -->
                <section class="json-input-section" aria-labelledby="json-input-title">
                    <div class="section-header">
                        <h2 id="json-input-title">JSON Input</h2>
                        <div class="section-actions">
                            <button id="load-example" class="primary-button" title="Load example JSON">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10,9 9,9 8,9"></polyline>
                                </svg>
                                Example
                            </button>
                            <button id="format-json" class="secondary-button" title="Format JSON">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="16,18 22,12 16,6"></polyline>
                                    <polyline points="8,6 2,12 8,18"></polyline>
                                </svg>
                                Format
                            </button>
                            <button id="clear-json" class="secondary-button" title="Clear JSON">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"></polyline>
                                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                                </svg>
                                Clear
                            </button>
                        </div>
                    </div>
                    <div class="json-editor-container">
                        <div id="origJsonContainer" class="json-editor" role="textbox" aria-label="JSON input editor"></div>
                    </div>
                </section>
                <!-- Configuration Section -->
                <section class="config-section" aria-labelledby="config-title">
                    <div class="section-header">
                        <h2 id="config-title">Configuration</h2>
                        <div class="section-actions">
                            <button id="generate-code" class="primary-button" title="Generate Dart code">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="16,18 22,12 16,6"></polyline>
                                    <polyline points="8,6 2,12 8,18"></polyline>
                                </svg>
                                Generate
                            </button>
                            <button id="test-code" class="secondary-button" title="Test code display" style="display: none;">
                                Test
                            </button>
                            <button id="reset-config" class="secondary-button" title="Reset to defaults">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"></polyline>
                                    <path d="M20.49,15a9,9,0,1,1-2.12-9.36L23,10"></path>
                                </svg>
                                Reset
                            </button>
                        </div>
                    </div>

                    <div class="config-grid">
                        <!-- JSON Key Options -->
                        <div class="config-group">
                            <h3 class="config-group-title">JSON Key Options</h3>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="usingJsonKeyCheckBox" name="usingJsonKeyCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Use JSON key annotations</span>
                                </label>
                                <label class="checkbox-label checkbox-sub">
                                    <input type="checkbox" id="jsonKeyPrivateCheckBox" name="jsonKeyPrivateCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Private JSON key constants</span>
                                </label>
                            </div>
                        </div>

                        <!-- Code Style Options -->
                        <div class="config-group">
                            <h3 class="config-group-title">Code Style</h3>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="camelCheckBox" name="camelCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Convert snake_case to camelCase</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="nullSafeCheckBox" name="nullSafeCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Enable null safety</span>
                                </label>
                            </div>
                        </div>

                        <!-- Type Handling -->
                        <div class="config-group">
                            <h3 class="config-group-title">Type Handling</h3>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="faultToleranceCheckBox" name="faultToleranceCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Fault tolerance for mismatched types</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="forceStringCheckBox" name="forceStringCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Force String type (except bool)</span>
                                </label>
                            </div>
                        </div>

                        <!-- Additional Features -->
                        <div class="config-group">
                            <h3 class="config-group-title">Additional Features</h3>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="origJsonCheckBox" name="origJsonCheckBox">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">Store original JSON</span>
                                </label>
                            </div>
                        </div>

                        <!-- Class Configuration -->
                        <div class="config-group config-group-full">
                            <h3 class="config-group-title">Class Configuration</h3>
                            <div class="input-group">
                                <label for="classNameTextField" class="input-label">Root Class Name</label>
                                <input type="text" id="classNameTextField" name="className" class="text-input" placeholder="e.g., UserModel, ApiResponse" autocomplete="off">
                            </div>
                            <div class="input-group">
                                <label for="fileNameTextField" class="input-label">Generated File Name</label>
                                <input type="text" id="fileNameTextField" name="fileName" class="text-input" placeholder="Auto-generated from class name" readonly>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Dart Output Section -->
            <section class="dart-output-section" aria-labelledby="dart-output-title">
                <div class="section-header">
                    <h2 id="dart-output-title">Generated Dart Code</h2>
                    <div class="section-actions">
                        <button id="download-file" class="secondary-button" title="Download as .dart file">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Download
                        </button>
                        <button id="copyFileBtn" class="primary-button" title="Copy to clipboard">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="m5,15H4a2,2 0 0,1 -2,-2V4a2,2 0 0,1 2,-2H13a2,2 0 0,1 2,2v1"></path>
                            </svg>
                            Copy Code
                        </button>
                    </div>
                </div>
                <div class="code-output-container">
                    <pre class="code-output" style="display: none;"><code id="dartCode" class="language-dart"></code></pre>
                    <div id="code-placeholder" class="code-placeholder" style="display: block;">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <polyline points="16,18 22,12 16,6"></polyline>
                            <polyline points="8,6 2,12 8,18"></polyline>
                        </svg>
                        <h3>Your Dart code will appear here</h3>
                        <p>Enter valid JSON in the input section to generate Dart model classes</p>
                    </div>
                </div>
            </section>
        </main>
    </div>
    <!-- External Dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.7.0/jsoneditor.min.js" integrity="sha512-T9isxS19cp5TyUWFgUh67kjuTcafB/OyIxav2G1whJda6cmiGidGtqZUl1Iep+Hs6tS8kvl5c+MT4xfWuUUv0w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.7.0/jsoneditor.css" integrity="sha512-+dwBKGKkHTEGq+HPC9veK/RMCoS7iHZPy337aQsm2iiz0nSm5yp6WkbK7+UjlTTlCADz9hliigkfmq9fhf29Wg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css" integrity="sha512-0aPQyyeZrWj9sCA46UlmWgKOP0mUipLQ6OZXu8l4IcAmD2u31EPEy9VcIMvl7SoAaKe8bLXZhYoMaE/in+gcgA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css" integrity="sha512-rO+olRTkcf304DQBxSWxln8JXCzTHlKnIdnMUwYvQa9/Jd4cQaNkItIUj6Z4nvW1dqK0SKXLbn9h4KwZTNtAyw==" crossorigin="anonymous" referrerpolicy="no-referrer" media="(prefers-color-scheme: dark)" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js" integrity="sha512-D9gUyxqja7hBtkWpPWGt9wfbfaMGVt9gnyCvYa+jojwwPHLCzUm5i8rpk7vD7wNee9bA35eYIjobYPaQuKS1MQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/dart.min.js" integrity="sha512-9fUlTdKUBnHYE5VGpQ4kLUBjpgJFXfy0k3RQYdEoNOXuFN1EI/9RMwR9enHhWH8gPG8t2xX3QMQqFnHCpOXzw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Application Scripts -->
    <script type="module" src="js/app.js"></script>

    <!-- Service Worker Registration -->
    <script>
        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('Service Worker registered successfully:', registration.scope);

                    // Handle service worker updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New content is available, show update notification
                                showUpdateNotification();
                            }
                        });
                    });
                } catch (error) {
                    console.error('Service Worker registration failed:', error);
                }
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        const installBanner = document.getElementById('pwa-install-banner');
        const installButton = document.getElementById('pwa-install-button');
        const dismissButton = document.getElementById('pwa-install-dismiss');

        window.addEventListener('beforeinstallprompt', (e) => {
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            // Stash the event so it can be triggered later
            deferredPrompt = e;
            // Show the install banner
            if (installBanner) {
                installBanner.style.display = 'block';
                installBanner.classList.add('fade-in');
            }
        });

        if (installButton) {
            installButton.addEventListener('click', async () => {
                if (deferredPrompt) {
                    // Show the install prompt
                    deferredPrompt.prompt();
                    // Wait for the user to respond to the prompt
                    const { outcome } = await deferredPrompt.userChoice;
                    console.log(`User response to the install prompt: ${outcome}`);
                    // Clear the deferredPrompt variable
                    deferredPrompt = null;
                    // Hide the install banner
                    if (installBanner) {
                        installBanner.style.display = 'none';
                    }
                }
            });
        }

        if (dismissButton) {
            dismissButton.addEventListener('click', () => {
                if (installBanner) {
                    installBanner.style.display = 'none';
                }
                // Remember user dismissed the prompt
                localStorage.setItem('pwa-install-dismissed', 'true');
            });
        }

        // Check if user previously dismissed the install prompt
        if (localStorage.getItem('pwa-install-dismissed') === 'true') {
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                return false;
            });
        }

        // Handle app installation
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            if (installBanner) {
                installBanner.style.display = 'none';
            }
            // Show success message
            if (window.jsonToDartApp && window.jsonToDartApp.uiManager) {
                window.jsonToDartApp.uiManager.showToast('App installed successfully!', 'success');
            }
        });

        // Show update notification
        function showUpdateNotification() {
            if (window.jsonToDartApp && window.jsonToDartApp.uiManager) {
                window.jsonToDartApp.uiManager.showToast(
                    'A new version is available. Refresh to update.',
                    'info',
                    8000
                );
            }
        }

        // Handle file sharing (if supported)
        if ('launchQueue' in window) {
            window.launchQueue.setConsumer((launchParams) => {
                if (launchParams.files && launchParams.files.length) {
                    handleSharedFiles(launchParams.files);
                }
            });
        }

        // Handle shared files
        async function handleSharedFiles(files) {
            for (const fileHandle of files) {
                const file = await fileHandle.getFile();
                if (file.type === 'application/json' || file.name.endsWith('.json')) {
                    const text = await file.text();
                    // Load the JSON into the editor
                    if (window.jsonToDartApp && window.jsonToDartApp.jsonEditor) {
                        try {
                            const jsonObj = JSON.parse(text);
                            window.jsonToDartApp.jsonEditor.set(jsonObj);
                            window.jsonToDartApp.uiManager.showToast('JSON file loaded successfully!', 'success');
                        } catch (error) {
                            window.jsonToDartApp.uiManager.showToast('Invalid JSON file', 'error');
                        }
                    }
                }
            }
        }

        // Handle URL parameters for sharing
        window.addEventListener('load', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const sharedJson = urlParams.get('json') || urlParams.get('text');

            if (sharedJson) {
                try {
                    const jsonObj = JSON.parse(decodeURIComponent(sharedJson));
                    // Wait for app to initialize, then load the JSON
                    setTimeout(() => {
                        if (window.jsonToDartApp && window.jsonToDartApp.jsonEditor) {
                            window.jsonToDartApp.jsonEditor.set(jsonObj);
                            window.jsonToDartApp.uiManager.showToast('Shared JSON loaded!', 'success');
                        }
                    }, 1000);
                } catch (error) {
                    console.error('Failed to parse shared JSON:', error);
                }
            }
        });
    </script>
</body>

</html>
