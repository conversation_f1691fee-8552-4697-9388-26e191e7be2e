/**
 * JSON to Dart Model Generator - Main Application
 * Modern ES6+ implementation with modular architecture
 */

import { JsonToDartConverter } from './converter.js';
import { UIManager } from './ui-manager.js';
import { StorageManager } from './storage-manager.js';
import { ThemeManager } from './theme-manager.js';

class JsonToDartApp {
  constructor() {
    this.converter = new JsonToDartConverter();
    this.uiManager = new UIManager();
    this.storageManager = new StorageManager();
    this.themeManager = new ThemeManager();
    
    this.jsonEditor = null;
    this.isInitialized = false;
    
    // Bind methods to preserve context
    this.handleJsonChange = this.handleJsonChange.bind(this);
    this.handleConfigChange = this.handleConfigChange.bind(this);
    this.handleCopyCode = this.handleCopyCode.bind(this);
    this.handleDownloadFile = this.handleDownloadFile.bind(this);
    this.handleFormatJson = this.handleFormatJson.bind(this);
    this.handleClearJson = this.handleClearJson.bind(this);
    this.handleResetConfig = this.handleResetConfig.bind(this);
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      this.uiManager.showLoading('Initializing application...');
      
      // Initialize theme first
      await this.themeManager.init();
      
      // Initialize JSON editor
      await this.initJsonEditor();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Load saved configuration
      this.loadConfiguration();

      // Load saved JSON
      this.loadSavedJson();

      // Ensure default class name is set if empty
      const classNameInput = document.getElementById('classNameTextField');
      if (classNameInput && !classNameInput.value.trim()) {
        classNameInput.value = 'SomeRootEntity';
      }

      // Generate initial code after a short delay to ensure everything is loaded
      setTimeout(() => {
        this.generateDartCode();
      }, 500);
      
      this.isInitialized = true;
      this.uiManager.hideLoading();

      console.log('JSON to Dart Model Generator initialized successfully');

      // Test JSON editor functionality
      this.testJsonEditor();
    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.uiManager.hideLoading();
      this.uiManager.showToast('Failed to initialize application. Please refresh the page.', 'error');
    }
  }

  /**
   * Initialize the JSON editor
   */
  async initJsonEditor() {
    const container = document.getElementById('origJsonContainer');
    const fallbackEditor = document.getElementById('fallbackJsonEditor');

    if (!container) {
      throw new Error('JSON editor container not found');
    }

    // Try to initialize JSONEditor
    try {
      const options = {
        mode: 'code',
        theme: this.themeManager.getCurrentTheme() === 'dark' ? 'ace/theme/monokai' : 'ace/theme/github',
        onChange: () => {
          // Handle JSON changes
          this.handleJsonChange();
        },
        onChangeText: (jsonString) => {
          // Handle text changes
          this.handleJsonTextChange(jsonString);
        },
        onError: (error) => {
          console.warn('JSON Editor error:', error);
        },
        search: false,
        statusBar: false,
        navigationBar: false,
        mainMenuBar: false
      };

      this.jsonEditor = new JSONEditor(container, options);
      this.usingFallbackEditor = false;

      console.log('JSONEditor initialized successfully');

    } catch (error) {
      console.warn('JSONEditor failed to initialize, using fallback:', error);

      // Use fallback textarea editor
      if (fallbackEditor) {
        container.style.display = 'none';
        fallbackEditor.style.display = 'block';
        this.usingFallbackEditor = true;

        // Setup fallback editor events
        fallbackEditor.addEventListener('input', (e) => {
          this.handleJsonTextChange(e.target.value);
        });

        this.jsonEditor = {
          set: (obj) => {
            fallbackEditor.value = JSON.stringify(obj, null, 2);
          },
          get: () => {
            return JSON.parse(fallbackEditor.value);
          },
          getText: () => {
            return fallbackEditor.value;
          },
          setText: (text) => {
            fallbackEditor.value = text;
          }
        };

        // If fallback editor already has content, keep it
        if (!fallbackEditor.value.trim()) {
          // Set default content if empty
          const defaultJson = this.getDefaultJsonObject();
          fallbackEditor.value = JSON.stringify(defaultJson, null, 2);
        }

        console.log('Fallback editor initialized');
      } else {
        throw new Error('Both JSONEditor and fallback editor failed to initialize');
      }
    }

    // Set initial JSON content after a short delay
    setTimeout(() => {
      this.loadInitialJson();
    }, 100);
  }

  /**
   * Setup all event listeners
   */
  setupEventListeners() {
    // Configuration checkboxes
    const checkboxes = [
      'usingJsonKeyCheckBox',
      'jsonKeyPrivateCheckBox',
      'camelCheckBox',
      'nullSafeCheckBox',
      'faultToleranceCheckBox',
      'forceStringCheckBox',
      'origJsonCheckBox'
    ];

    checkboxes.forEach(id => {
      const checkbox = document.getElementById(id);
      if (checkbox) {
        checkbox.addEventListener('change', this.handleConfigChange);
      }
    });

    // Text inputs
    const classNameInput = document.getElementById('classNameTextField');
    if (classNameInput) {
      classNameInput.addEventListener('input', this.handleConfigChange);
    }

    // Buttons
    const copyBtn = document.getElementById('copyFileBtn');
    if (copyBtn) {
      copyBtn.addEventListener('click', this.handleCopyCode);
    }

    const downloadBtn = document.getElementById('download-file');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', this.handleDownloadFile);
    }

    const formatBtn = document.getElementById('format-json');
    if (formatBtn) {
      formatBtn.addEventListener('click', this.handleFormatJson);
    }

    const clearBtn = document.getElementById('clear-json');
    if (clearBtn) {
      clearBtn.addEventListener('click', this.handleClearJson);
    }

    const resetBtn = document.getElementById('reset-config');
    if (resetBtn) {
      resetBtn.addEventListener('click', this.handleResetConfig);
    }

    const generateBtn = document.getElementById('generate-code');
    if (generateBtn) {
      generateBtn.addEventListener('click', () => {
        this.generateDartCode();
      });
    }

    const loadExampleBtn = document.getElementById('load-example');
    if (loadExampleBtn) {
      loadExampleBtn.addEventListener('click', () => {
        this.loadDefaultJson();
      });
    }

    // Test button for debugging (hidden by default)
    const testBtn = document.getElementById('test-code');
    if (testBtn) {
      testBtn.addEventListener('click', () => {
        const testCode = `class TestModel {
  String? name;
  int? age;

  TestModel({this.name, this.age});

  TestModel.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    age = json['age']?.toInt();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['age'] = age;
    return data;
  }
}`;
        this.uiManager.showGeneratedCode(testCode);
      });
    }

    // Info banner close button
    const infoBannerClose = document.getElementById('info-close');
    if (infoBannerClose) {
      infoBannerClose.addEventListener('click', () => {
        this.uiManager.hideInfoBanner();
      });
    }

    // Special handling for dependent checkboxes
    const usingJsonKeyCheckBox = document.getElementById('usingJsonKeyCheckBox');
    const jsonKeyPrivateCheckBox = document.getElementById('jsonKeyPrivateCheckBox');
    
    if (usingJsonKeyCheckBox && jsonKeyPrivateCheckBox) {
      usingJsonKeyCheckBox.addEventListener('change', () => {
        jsonKeyPrivateCheckBox.disabled = !usingJsonKeyCheckBox.checked;
        if (!usingJsonKeyCheckBox.checked) {
          jsonKeyPrivateCheckBox.checked = false;
        }
      });
    }

    // Theme change listener
    this.themeManager.onThemeChange((theme) => {
      if (this.jsonEditor) {
        // Update JSON editor theme
        const editorTheme = theme === 'dark' ? 'ace/theme/monokai' : 'ace/theme/github';
        // Note: JSONEditor might not support theme switching directly
        // This would need to be implemented based on the specific editor being used
      }
    });
  }

  /**
   * Handle JSON content changes (when JSON object changes)
   */
  handleJsonChange() {
    if (!this.isInitialized) return;

    try {
      const jsonContent = this.jsonEditor.getText();
      this.handleJsonTextChange(jsonContent);
    } catch (error) {
      console.warn('Error getting JSON text:', error);
    }
  }

  /**
   * Handle JSON text changes
   */
  handleJsonTextChange(jsonString) {
    if (!this.isInitialized) return;

    // Save to storage
    this.storageManager.saveJsonContent(jsonString);

    // Debounce the generation to avoid excessive processing
    clearTimeout(this.generateTimeout);
    this.generateTimeout = setTimeout(() => {
      this.generateDartCode();
    }, 500);
  }

  /**
   * Handle configuration changes
   */
  handleConfigChange() {
    if (!this.isInitialized) return;
    
    // Save configuration
    this.saveConfiguration();
    
    // Regenerate code
    this.generateDartCode();
  }

  /**
   * Generate Dart code from current JSON and configuration
   */
  generateDartCode() {
    try {
      // Get JSON content
      let jsonContent;
      try {
        // Try to get as object first
        jsonContent = this.jsonEditor.get();
      } catch (error) {
        try {
          // If that fails, try to parse the text
          const jsonText = this.jsonEditor.getText();
          jsonContent = JSON.parse(jsonText);
        } catch (parseError) {
          // Handle JSON parsing errors
          this.uiManager.showCodeError('Invalid JSON: ' + parseError.message);
          return;
        }
      }

      // Ensure we have valid JSON content
      if (!jsonContent || typeof jsonContent !== 'object') {
        this.uiManager.showCodeError('Please enter valid JSON data');
        return;
      }

      // Get configuration
      const config = this.getConfiguration();

      // Generate Dart code
      const dartCode = this.converter.convert(jsonContent, config);

      // Update UI
      this.uiManager.showGeneratedCode(dartCode);
      this.updateFileName(config.className);

      // Hide any previous errors
      this.uiManager.hideInfoBanner();

    } catch (error) {
      console.error('Error generating Dart code:', error);
      this.uiManager.showCodeError('Error generating Dart code: ' + error.message);
    }
  }

  /**
   * Get current configuration from UI
   */
  getConfiguration() {
    return {
      className: document.getElementById('classNameTextField')?.value || 'SomeRootEntity',
      usingJsonKey: document.getElementById('usingJsonKeyCheckBox')?.checked || false,
      jsonKeyPrivate: document.getElementById('jsonKeyPrivateCheckBox')?.checked || false,
      camelCase: document.getElementById('camelCheckBox')?.checked || true,
      nullSafe: document.getElementById('nullSafeCheckBox')?.checked || false,
      faultTolerance: document.getElementById('faultToleranceCheckBox')?.checked || false,
      forceString: document.getElementById('forceStringCheckBox')?.checked || false,
      storeOriginalJson: document.getElementById('origJsonCheckBox')?.checked || false
    };
  }

  /**
   * Save current configuration to storage
   */
  saveConfiguration() {
    const config = this.getConfiguration();
    this.storageManager.saveConfiguration(config);
  }

  /**
   * Load configuration from storage
   */
  loadConfiguration() {
    const config = this.storageManager.loadConfiguration();
    
    // Apply configuration to UI
    Object.entries(config).forEach(([key, value]) => {
      switch (key) {
        case 'className':
          const classNameInput = document.getElementById('classNameTextField');
          if (classNameInput) classNameInput.value = value;
          break;
        case 'usingJsonKey':
          const usingJsonKeyCheckBox = document.getElementById('usingJsonKeyCheckBox');
          if (usingJsonKeyCheckBox) usingJsonKeyCheckBox.checked = value;
          break;
        case 'jsonKeyPrivate':
          const jsonKeyPrivateCheckBox = document.getElementById('jsonKeyPrivateCheckBox');
          if (jsonKeyPrivateCheckBox) {
            jsonKeyPrivateCheckBox.checked = value;
            jsonKeyPrivateCheckBox.disabled = !config.usingJsonKey;
          }
          break;
        case 'camelCase':
          const camelCheckBox = document.getElementById('camelCheckBox');
          if (camelCheckBox) camelCheckBox.checked = value;
          break;
        case 'nullSafe':
          const nullSafeCheckBox = document.getElementById('nullSafeCheckBox');
          if (nullSafeCheckBox) nullSafeCheckBox.checked = value;
          break;
        case 'faultTolerance':
          const faultToleranceCheckBox = document.getElementById('faultToleranceCheckBox');
          if (faultToleranceCheckBox) faultToleranceCheckBox.checked = value;
          break;
        case 'forceString':
          const forceStringCheckBox = document.getElementById('forceStringCheckBox');
          if (forceStringCheckBox) forceStringCheckBox.checked = value;
          break;
        case 'storeOriginalJson':
          const origJsonCheckBox = document.getElementById('origJsonCheckBox');
          if (origJsonCheckBox) origJsonCheckBox.checked = value;
          break;
      }
    });
  }

  /**
   * Load initial JSON content
   */
  loadInitialJson() {
    console.log('Loading initial JSON...');
    const savedJson = this.storageManager.loadJsonContent();
    if (savedJson && savedJson.trim()) {
      try {
        const jsonObj = JSON.parse(savedJson);
        this.jsonEditor.set(jsonObj);
        console.log('Loaded saved JSON');
      } catch (error) {
        console.warn('Failed to parse saved JSON:', error);
        this.setDefaultJson();
      }
    } else {
      console.log('No saved JSON, loading default');
      this.setDefaultJson();
    }
  }

  /**
   * Load saved JSON content (called from init)
   */
  loadSavedJson() {
    // This is now handled by loadInitialJson
    // Just trigger code generation
    setTimeout(() => {
      this.generateDartCode();
    }, 300);
  }

  /**
   * Get default JSON object
   */
  getDefaultJsonObject() {
    return {
      "user_id": 12345,
      "user_name": "John Doe",
      "email": "<EMAIL>",
      "is_active": true,
      "created_at": "2024-01-15T10:30:00Z",
      "profile": {
        "first_name": "John",
        "last_name": "Doe",
        "age": 30,
        "location": "New York",
        "bio": "Software developer passionate about Flutter and Dart"
      },
      "preferences": {
        "theme": "dark",
        "notifications_enabled": true,
        "language": "en"
      },
      "tags": ["developer", "flutter", "dart", "mobile"],
      "scores": [95.5, 87.2, 92.1, 88.9],
      "social_links": [
        {
          "platform": "github",
          "url": "https://github.com/johndoe",
          "verified": true
        },
        {
          "platform": "linkedin",
          "url": "https://linkedin.com/in/johndoe",
          "verified": false
        }
      ],
      "metadata": {
        "last_login": "2024-01-20T15:45:30Z",
        "login_count": 42,
        "device_info": {
          "platform": "iOS",
          "version": "17.2",
          "device_id": "ABC123XYZ"
        }
      }
    };
  }

  /**
   * Set default JSON example
   */
  setDefaultJson() {
    const defaultJson = this.getDefaultJsonObject();

    if (this.jsonEditor) {
      this.jsonEditor.set(defaultJson);
      console.log('Set default JSON');
    }
  }

  /**
   * Load default JSON (for button click)
   */
  loadDefaultJson() {
    this.setDefaultJson();
    // Trigger code generation
    setTimeout(() => {
      this.generateDartCode();
    }, 200);
  }

  /**
   * Test JSON editor functionality
   */
  testJsonEditor() {
    setTimeout(() => {
      console.log('Testing JSON editor...');
      console.log('JSON Editor object:', this.jsonEditor);
      console.log('Using fallback editor:', this.usingFallbackEditor);

      if (this.jsonEditor) {
        try {
          const content = this.jsonEditor.get();
          console.log('JSON content:', content);
          console.log('JSON content keys:', Object.keys(content || {}));
        } catch (error) {
          console.log('Error getting JSON content:', error);
          try {
            const text = this.jsonEditor.getText();
            console.log('JSON text:', text ? text.substring(0, 100) + '...' : 'empty');
          } catch (textError) {
            console.log('Error getting JSON text:', textError);
          }
        }
      }
    }, 1000);
  }

  /**
   * Update the generated file name
   */
  updateFileName(className) {
    const fileNameInput = document.getElementById('fileNameTextField');
    if (fileNameInput && className) {
      const fileName = className
        .replace(/([A-Z])/g, "_$1")
        .toLowerCase()
        .replace(/^_/, '') + '.dart';
      fileNameInput.value = fileName;
    }
  }

  /**
   * Handle copy code button click
   */
  async handleCopyCode() {
    try {
      const dartCode = this.uiManager.getCurrentCode();
      if (!dartCode) {
        this.uiManager.showToast('No code to copy', 'warning');
        return;
      }

      await navigator.clipboard.writeText(dartCode);
      this.uiManager.showToast('Code copied to clipboard!', 'success');
    } catch (error) {
      console.error('Failed to copy code:', error);
      this.uiManager.showToast('Failed to copy code to clipboard', 'error');
    }
  }

  /**
   * Handle download file button click
   */
  handleDownloadFile() {
    const dartCode = this.uiManager.getCurrentCode();
    const fileName = document.getElementById('fileNameTextField')?.value || 'model.dart';
    
    if (!dartCode) {
      this.uiManager.showToast('No code to download', 'warning');
      return;
    }

    try {
      const blob = new Blob([dartCode], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      this.uiManager.showToast('File downloaded successfully!', 'success');
    } catch (error) {
      console.error('Failed to download file:', error);
      this.uiManager.showToast('Failed to download file', 'error');
    }
  }

  /**
   * Handle format JSON button click
   */
  handleFormatJson() {
    try {
      // Try to get and reformat the JSON
      let jsonContent;
      try {
        jsonContent = this.jsonEditor.get();
      } catch (error) {
        // If get() fails, try to parse the text
        const jsonText = this.jsonEditor.getText();
        jsonContent = JSON.parse(jsonText);
      }

      // Set the formatted JSON back
      this.jsonEditor.set(jsonContent);
      this.uiManager.showToast('JSON formatted successfully!', 'success');
    } catch (error) {
      console.error('Failed to format JSON:', error);
      this.uiManager.showToast('Invalid JSON - cannot format', 'error');
    }
  }

  /**
   * Handle clear JSON button click
   */
  handleClearJson() {
    if (confirm('Are you sure you want to clear the JSON input?')) {
      this.jsonEditor.set({});
      this.uiManager.showToast('JSON cleared', 'info');
    }
  }

  /**
   * Handle reset configuration button click
   */
  handleResetConfig() {
    if (confirm('Are you sure you want to reset all configuration to defaults?')) {
      this.storageManager.clearConfiguration();
      this.loadConfiguration();
      this.generateDartCode();
      this.uiManager.showToast('Configuration reset to defaults', 'info');
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const app = new JsonToDartApp();
  app.init();
  
  // Make app globally available for debugging
  window.jsonToDartApp = app;
});
