/**
 * JSON to Dart Model Converter
 * Handles the conversion logic from JSON to Dart model classes
 */

export class JsonToDartConverter {
  constructor() {
    this.dartKeywords = [
      "num", "double", "int", "String", "bool", "List", "abstract", "dynamic", 
      "implements", "show", "as", "else", "import", "static", "assert", "enum", 
      "in", "super", "async", "export", "interface", "switch", "await", "extends", 
      "is", "sync", "break", "external", "library", "this", "case", "factory", 
      "mixin", "throw", "catch", "false", "new", "true", "class", "final", "null", 
      "try", "const", "finally", "on", "typedef", "continue", "for", "operator", 
      "var", "covariant", "Function", "part", "void", "default", "get", "rethrow", 
      "while", "deferred", "hide", "return", "with", "do", "if", "set", "yield"
    ];
  }

  /**
   * Convert JSON object to Dart model classes
   * @param {Object} jsonObj - The JSON object to convert
   * @param {Object} config - Configuration options
   * @returns {string} Generated Dart code
   */
  convert(jsonObj, config) {
    if (!jsonObj || typeof jsonObj !== 'object') {
      throw new Error('Invalid JSON object provided');
    }

    // Remove surplus elements from arrays (keep only first element for structure)
    this.removeSurplusElements(jsonObj);

    // Generate the Dart code
    const dartCode = this.objToDart(jsonObj, '', config.className || 'SomeRootEntity', config);
    
    // Add header comment
    const header = `///\n/// Code generated by JSON to Dart Model Generator\n/// https://ashamp.github.io/jsonToDartModel/\n///\n\n`;
    
    return header + dartCode;
  }

  /**
   * Remove surplus elements from arrays to keep only the structure
   * @param {*} obj - Object to process
   */
  removeSurplusElements(obj) {
    if (Array.isArray(obj)) {
      obj.length = Math.min(obj.length, 1);
      if (obj.length > 0) {
        this.removeSurplusElements(obj[0]);
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          this.removeSurplusElements(obj[key]);
        }
      }
    }
  }

  /**
   * Convert snake_case to camelCase
   * @param {string} str - String to convert
   * @returns {string} Converted string
   */
  snakeToCamel(str) {
    return str.replace(/([-_][a-zA-Z])/g, (group) => 
      group.toUpperCase().replace('-', '').replace('_', '')
    );
  }

  /**
   * Capitalize first letter
   * @param {string} string - String to capitalize
   * @returns {string} Capitalized string
   */
  uppercaseFirst(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  /**
   * Protect against Dart keywords
   * @param {string} key - Property key to check
   * @returns {string} Safe property key
   */
  dartKeywordDefence(key) {
    if (typeof key === 'string') {
      const isStartWithNum = key.match(/^\d/);
      if (this.dartKeywords.includes(key) || isStartWithNum) {
        return `the${this.uppercaseFirst(key)}`;
      }
    }
    return key;
  }

  /**
   * Generate generic string for List types
   * @param {string} innerClass - Inner class type
   * @param {number} count - Nesting level
   * @returns {string} Generic type string
   */
  generateGenericString(innerClass, count) {
    const genericStrings = [innerClass];
    while (count > 0) {
      genericStrings.unshift('List<');
      genericStrings.push('>');
      count--;
    }
    return genericStrings.join('');
  }

  /**
   * Get information about the innermost object in nested arrays
   * @param {Array} arr - Array to analyze
   * @param {string} className - Class name for objects
   * @param {Object} config - Configuration options
   * @returns {Object} Information about inner object
   */
  getInnerObjInfo(arr, className, config) {
    let count = 0;
    
    const getInnerObj = (arr) => {
      if (Array.isArray(arr)) {
        count++;
        return getInnerObj(arr[0]);
      }
      return arr;
    };

    const inner = getInnerObj(arr);
    let innerClass = className;

    if (typeof inner === 'object' && inner !== null) {
      // Keep as object class
    } else if (typeof inner === 'boolean') {
      innerClass = 'bool';
    } else if (typeof inner === 'string') {
      innerClass = config.forceString ? 'String' : 'String';
    } else if (typeof inner === 'number') {
      if (config.forceString) {
        innerClass = 'String';
      } else {
        innerClass = Number.isInteger(inner) ? 'int' : 'double';
      }
    }

    return { inner, innerClass, count };
  }

  /**
   * Generate iteration lines for array processing
   * @param {Array} arr - Array to process
   * @param {string} className - Class name
   * @param {string} key - Property key
   * @param {string} legalKey - Legal property key
   * @param {string} jsonKey - JSON key reference
   * @param {Object} config - Configuration options
   * @returns {Object} Generated code lines
   */
  getIterateLines(arr, className, key, legalKey, jsonKey, config) {
    if (legalKey === 'data') {
      legalKey = 'this.data';
    }

    const makeBlank = (count) => '  '.repeat(count + 1);

    const { inner, innerClass, count } = this.getInnerObjInfo(arr, className, config);
    
    if (inner === undefined || inner === null) {
      throw new Error(`The property named '${key}' is an EMPTY array! Parse process failed!`);
    }

    const total = count;
    const fromJsonLines = [];
    const toJsonLines = [];
    let currentCount = count - 1;

    if (typeof inner === 'object' && inner !== null) {
      fromJsonLines.push(`${makeBlank(currentCount * 3)}v.forEach((v) {\n${makeBlank(currentCount * 4)}arr${currentCount}.add(${className}.fromJson(v));\n${makeBlank(currentCount * 3)}});`);
      toJsonLines.push(`${makeBlank(currentCount * 3)}v${config.nullSafe ? '!' : ''}.forEach((v) {\n${makeBlank(currentCount * 4)}arr${currentCount}.add(v${config.nullSafe ? '!' : ''}.toJson());\n${makeBlank(currentCount * 3)}});`);
    } else {
      let toType = 'v';
      
      if (typeof inner === 'string') {
        toType = 'v.toString()';
      } else if (typeof inner === 'number') {
        if (config.forceString) {
          toType = 'v.toString()';
        } else if (Number.isInteger(inner)) {
          toType = config.faultTolerance ? 'int.tryParse(v.toString() ?? \'\')' : 'v.toInt()';
        } else {
          toType = config.faultTolerance ? 'double.tryParse(v.toString() ?? \'\')' : 'v.toDouble()';
        }
      }

      fromJsonLines.push(`${makeBlank(currentCount * 3)}v.forEach((v) {\n${makeBlank(currentCount * 4)}arr${currentCount}.add(${toType});\n${makeBlank(currentCount * 3)}});`);
      toJsonLines.push(`${makeBlank(currentCount * 3)}v${config.nullSafe ? '!' : ''}.forEach((v) {\n${makeBlank(currentCount * 4)}arr${currentCount}.add(v);\n${makeBlank(currentCount * 3)}});`);
    }

    while (currentCount > 0) {
      fromJsonLines.unshift(`${makeBlank(currentCount * 2)}v.forEach((v) {\n${makeBlank(currentCount * 3)}final arr${currentCount} = ${this.generateGenericString(innerClass, total - currentCount).slice(4)}[];`);
      fromJsonLines.push(`${makeBlank(currentCount * 3)}arr${currentCount - 1}.add(arr${currentCount});\n${makeBlank(currentCount * 2)}});`);
      toJsonLines.unshift(`${makeBlank(currentCount * 2)}v${config.nullSafe ? '!' : ''}.forEach((v) {\n${makeBlank(currentCount * 3)}final arr${currentCount} = [];`);
      toJsonLines.push(`${makeBlank(currentCount * 3)}arr${currentCount - 1}.add(arr${currentCount});\n${makeBlank(currentCount * 2)}});`);
      currentCount--;
    }

    const typeCheck = config.faultTolerance ? ` && (json[${jsonKey}] is List)` : '';
    fromJsonLines.unshift(`    if (json[${jsonKey}] != null${typeCheck}) {\n      final v = json[${jsonKey}];\n      final arr0 = ${this.generateGenericString(innerClass, total).slice(4)}[];`);
    fromJsonLines.push(`      ${legalKey} = arr0;\n    }\n`);
    toJsonLines.unshift(`    if (${legalKey} != null) {\n      final v = ${legalKey};\n      final arr0 = [];`);
    toJsonLines.push(`      data[${jsonKey}] = arr0;\n    }\n`);

    return {
      fromJsonLinesJoined: fromJsonLines.join('\r\n'),
      toJsonLinesJoined: toJsonLines.join('\r\n')
    };
  }

  /**
   * Convert JSON object to Dart class
   * @param {Object} jsonObj - JSON object to convert
   * @param {string} prefix - Class name prefix
   * @param {string} baseClass - Base class name
   * @param {Object} config - Configuration options
   * @returns {string} Generated Dart class code
   */
  objToDart(jsonObj, prefix, baseClass, config) {
    if (Array.isArray(jsonObj)) {
      return this.objToDart(jsonObj[0], prefix, baseClass, config);
    }

    const lines = [];
    const jsonKeysLines = [];
    const propsLines = [];
    const constructorLines = [];
    const fromJsonLines = [];
    const toJsonLines = [];

    let className = `${prefix}${this.uppercaseFirst(baseClass)}`;
    if (config.camelCase) {
      className = this.snakeToCamel(className);
    }

    lines.push(`class ${className} {`);
    lines.push(`/*\r\n${JSON.stringify(jsonObj, null, 2)} \r\n*/\r\n`);

    constructorLines.push(`  ${className}({\n`);
    fromJsonLines.push(`  ${className}.fromJson(Map<String, dynamic> json) {\n`);
    
    if (config.storeOriginalJson) {
      fromJsonLines.push(`    __origJson = json;\n`);
    }
    
    toJsonLines.push(`  Map<String, dynamic> toJson() {\n`);
    toJsonLines.push(`    final data = <String, dynamic>{};\n`);

    for (const key in jsonObj) {
      if (jsonObj.hasOwnProperty(key)) {
        let element = jsonObj[key];
        let legalKey = this.dartKeywordDefence(key);

        if (config.camelCase) {
          legalKey = this.snakeToCamel(legalKey);
        }

        const thisData = key === 'data' ? 'this.' : '';
        let jsonKey = `'${key}'`;
        
        if (config.usingJsonKey) {
          jsonKey = `${config.jsonKeyPrivate ? '_' : ''}jsonKey${className}${this.uppercaseFirst(legalKey)}`;
          jsonKeysLines.push(`const String ${jsonKey} = '${key}';`);
        }

        constructorLines.push(`    this.${legalKey},\n`);

        if (element === null) {
          console.warn(`The Property named '${key}' is null, which will be treated as String type`);
          element = '';
        }

        if (typeof element === 'object') {
          const subClassName = `${className}${this.uppercaseFirst(key)}`;
          const finalSubClassName = config.camelCase ? this.snakeToCamel(subClassName) : subClassName;

          if (Array.isArray(element)) {
            const { inner, innerClass, count } = this.getInnerObjInfo(element, finalSubClassName, config);
            const { fromJsonLinesJoined, toJsonLinesJoined } = this.getIterateLines(element, finalSubClassName, key, legalKey, jsonKey, config);
            
            let genericString = this.generateGenericString(innerClass, count);
            if (config.nullSafe) {
              genericString = genericString.replace(/>/g, '?>') + '?';
            }
            
            propsLines.push(`  ${genericString} ${legalKey};\n`);
            fromJsonLines.push(fromJsonLinesJoined);
            toJsonLines.push(toJsonLinesJoined);
            
            if (typeof inner === 'object' && inner !== null) {
              lines.unshift(this.objToDart(element, className, key, config));
            }
          } else {
            lines.unshift(this.objToDart(element, className, key, config));
            propsLines.push(`  ${finalSubClassName}${config.nullSafe ? '?' : ''} ${legalKey};\n`);
            
            const typeCheck = config.faultTolerance ? ` && (json[${jsonKey}] is Map)` : '';
            fromJsonLines.push(`    ${legalKey} = (json[${jsonKey}] != null${typeCheck}) ? ${finalSubClassName}.fromJson(json[${jsonKey}]) : null;\n`);
            toJsonLines.push(`    if (${legalKey} != null) {\n      data[${jsonKey}] = ${thisData}${legalKey}${config.nullSafe ? '!' : ''}.toJson();\n    }\n`);
          }
        } else {
          let toType = `json[${jsonKey}]`;
          let type = '';

          if (typeof element === 'boolean') {
            type = 'bool';
          } else if (config.forceString || typeof element === 'string') {
            toType = `json[${jsonKey}]?.toString()`;
            type = 'String';
          } else if (typeof element === 'number') {
            if (Number.isInteger(element)) {
              toType = config.faultTolerance ? `int.tryParse(json[${jsonKey}]?.toString() ?? '')` : `json[${jsonKey}]?.toInt()`;
              type = 'int';
            } else {
              toType = config.faultTolerance ? `double.tryParse(json[${jsonKey}]?.toString() ?? '')` : `json[${jsonKey}]?.toDouble()`;
              type = 'double';
            }
          }

          propsLines.push(`  ${type}${config.nullSafe ? '?' : ''} ${legalKey};\n`);
          fromJsonLines.push(`    ${legalKey} = ${toType};\n`);
          toJsonLines.push(`    data[${jsonKey}] = ${thisData}${legalKey};\n`);
        }
      }
    }

    if (config.storeOriginalJson) {
      propsLines.push(`  Map<String, dynamic> __origJson = {};\n`);
    }

    if (config.usingJsonKey) {
      lines.unshift(jsonKeysLines.join('\n'));
    }

    constructorLines.push(`  });`);
    fromJsonLines.push(`  }`);
    toJsonLines.push(`    return data;\n  }`);

    lines.push(propsLines.join(''));
    lines.push(constructorLines.join(''));
    lines.push(fromJsonLines.join(''));
    lines.push(toJsonLines.join(''));
    
    if (config.storeOriginalJson) {
      lines.push(`  Map<String, dynamic> origJson() => __origJson;`);
    }

    lines.push(`}\n`);

    return lines.join('\r\n');
  }
}
