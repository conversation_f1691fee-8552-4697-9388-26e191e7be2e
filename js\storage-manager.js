/**
 * Storage Manager
 * Handles local storage operations for configuration and JSON content
 */

export class StorageManager {
  constructor() {
    this.storagePrefix = 'jsonToDart_';
    this.keys = {
      jsonContent: 'jsonEditor',
      configuration: 'config',
      theme: 'theme'
    };
    
    // Default configuration
    this.defaultConfig = {
      className: 'SomeRootEntity',
      usingJsonKey: false,
      jsonKeyPrivate: true,
      camelCase: true,
      nullSafe: false,
      faultTolerance: false,
      forceString: false,
      storeOriginalJson: false
    };
  }

  /**
   * Get storage key with prefix
   * @param {string} key - Storage key
   * @returns {string} Prefixed key
   */
  getStorageKey(key) {
    return this.storagePrefix + key;
  }

  /**
   * Check if localStorage is available
   * @returns {boolean} Whether localStorage is available
   */
  isStorageAvailable() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * Save data to localStorage
   * @param {string} key - Storage key
   * @param {*} data - Data to save
   * @returns {boolean} Success status
   */
  saveToStorage(key, data) {
    if (!this.isStorageAvailable()) {
      console.warn('localStorage is not available');
      return false;
    }

    try {
      const serializedData = JSON.stringify(data);
      localStorage.setItem(this.getStorageKey(key), serializedData);
      return true;
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
      return false;
    }
  }

  /**
   * Load data from localStorage
   * @param {string} key - Storage key
   * @param {*} defaultValue - Default value if key doesn't exist
   * @returns {*} Loaded data or default value
   */
  loadFromStorage(key, defaultValue = null) {
    if (!this.isStorageAvailable()) {
      return defaultValue;
    }

    try {
      const serializedData = localStorage.getItem(this.getStorageKey(key));
      if (serializedData === null) {
        return defaultValue;
      }
      return JSON.parse(serializedData);
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
      return defaultValue;
    }
  }

  /**
   * Remove data from localStorage
   * @param {string} key - Storage key
   * @returns {boolean} Success status
   */
  removeFromStorage(key) {
    if (!this.isStorageAvailable()) {
      return false;
    }

    try {
      localStorage.removeItem(this.getStorageKey(key));
      return true;
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
      return false;
    }
  }

  /**
   * Clear all application data from localStorage
   * @returns {boolean} Success status
   */
  clearAllStorage() {
    if (!this.isStorageAvailable()) {
      return false;
    }

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          localStorage.removeItem(key);
        }
      });
      return true;
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
      return false;
    }
  }

  /**
   * Save JSON content
   * @param {string} jsonContent - JSON content to save
   * @returns {boolean} Success status
   */
  saveJsonContent(jsonContent) {
    return this.saveToStorage(this.keys.jsonContent, jsonContent);
  }

  /**
   * Load JSON content
   * @returns {string|null} Saved JSON content or null
   */
  loadJsonContent() {
    return this.loadFromStorage(this.keys.jsonContent, null);
  }

  /**
   * Clear saved JSON content
   * @returns {boolean} Success status
   */
  clearJsonContent() {
    return this.removeFromStorage(this.keys.jsonContent);
  }

  /**
   * Save configuration
   * @param {Object} config - Configuration object
   * @returns {boolean} Success status
   */
  saveConfiguration(config) {
    // Merge with default config to ensure all properties exist
    const mergedConfig = { ...this.defaultConfig, ...config };
    return this.saveToStorage(this.keys.configuration, mergedConfig);
  }

  /**
   * Load configuration
   * @returns {Object} Configuration object
   */
  loadConfiguration() {
    const savedConfig = this.loadFromStorage(this.keys.configuration, {});
    // Merge with default config to ensure all properties exist
    return { ...this.defaultConfig, ...savedConfig };
  }

  /**
   * Clear configuration
   * @returns {boolean} Success status
   */
  clearConfiguration() {
    return this.removeFromStorage(this.keys.configuration);
  }

  /**
   * Save theme preference
   * @param {string} theme - Theme name
   * @returns {boolean} Success status
   */
  saveTheme(theme) {
    return this.saveToStorage(this.keys.theme, theme);
  }

  /**
   * Load theme preference
   * @returns {string} Theme name
   */
  loadTheme() {
    return this.loadFromStorage(this.keys.theme, 'light');
  }

  /**
   * Get storage usage information
   * @returns {Object} Storage usage info
   */
  getStorageInfo() {
    if (!this.isStorageAvailable()) {
      return {
        available: false,
        used: 0,
        total: 0,
        percentage: 0
      };
    }

    try {
      let used = 0;
      const keys = Object.keys(localStorage);
      
      keys.forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          used += localStorage.getItem(key).length;
        }
      });

      // Estimate total localStorage capacity (usually 5-10MB)
      const total = 5 * 1024 * 1024; // 5MB estimate
      const percentage = (used / total) * 100;

      return {
        available: true,
        used,
        total,
        percentage: Math.round(percentage * 100) / 100
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        available: false,
        used: 0,
        total: 0,
        percentage: 0
      };
    }
  }

  /**
   * Export all application data
   * @returns {Object} Exported data
   */
  exportData() {
    const data = {
      jsonContent: this.loadJsonContent(),
      configuration: this.loadConfiguration(),
      theme: this.loadTheme(),
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };

    return data;
  }

  /**
   * Import application data
   * @param {Object} data - Data to import
   * @returns {boolean} Success status
   */
  importData(data) {
    try {
      if (data.jsonContent) {
        this.saveJsonContent(data.jsonContent);
      }
      
      if (data.configuration) {
        this.saveConfiguration(data.configuration);
      }
      
      if (data.theme) {
        this.saveTheme(data.theme);
      }

      return true;
    } catch (error) {
      console.error('Failed to import data:', error);
      return false;
    }
  }

  /**
   * Validate configuration object
   * @param {Object} config - Configuration to validate
   * @returns {boolean} Whether configuration is valid
   */
  validateConfiguration(config) {
    if (!config || typeof config !== 'object') {
      return false;
    }

    // Check required properties
    const requiredProps = Object.keys(this.defaultConfig);
    for (const prop of requiredProps) {
      if (!(prop in config)) {
        return false;
      }
    }

    // Validate property types
    const validations = {
      className: (val) => typeof val === 'string',
      usingJsonKey: (val) => typeof val === 'boolean',
      jsonKeyPrivate: (val) => typeof val === 'boolean',
      camelCase: (val) => typeof val === 'boolean',
      nullSafe: (val) => typeof val === 'boolean',
      faultTolerance: (val) => typeof val === 'boolean',
      forceString: (val) => typeof val === 'boolean',
      storeOriginalJson: (val) => typeof val === 'boolean'
    };

    for (const [prop, validator] of Object.entries(validations)) {
      if (!validator(config[prop])) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get default configuration
   * @returns {Object} Default configuration
   */
  getDefaultConfiguration() {
    return { ...this.defaultConfig };
  }

  /**
   * Reset configuration to defaults
   * @returns {boolean} Success status
   */
  resetConfiguration() {
    return this.saveConfiguration(this.defaultConfig);
  }

  /**
   * Check if configuration exists
   * @returns {boolean} Whether configuration exists in storage
   */
  hasConfiguration() {
    const config = this.loadFromStorage(this.keys.configuration, null);
    return config !== null;
  }

  /**
   * Check if JSON content exists
   * @returns {boolean} Whether JSON content exists in storage
   */
  hasJsonContent() {
    const content = this.loadFromStorage(this.keys.jsonContent, null);
    return content !== null && content.trim() !== '';
  }
}
