/**
 * Theme Manager
 * Handles dark/light theme switching and persistence
 */

export class ThemeManager {
  constructor() {
    this.currentTheme = 'light';
    this.themeChangeCallbacks = [];
    this.storageKey = 'jsonToDart_theme';
    
    // Bind methods
    this.toggleTheme = this.toggleTheme.bind(this);
    this.handleSystemThemeChange = this.handleSystemThemeChange.bind(this);
  }

  /**
   * Initialize theme manager
   */
  async init() {
    // Load saved theme or detect system preference
    this.currentTheme = this.loadThemePreference();
    
    // Apply initial theme
    this.applyTheme(this.currentTheme);
    
    // Setup theme toggle button
    this.setupThemeToggle();
    
    // Listen for system theme changes
    this.setupSystemThemeListener();
    
    console.log('Theme manager initialized with theme:', this.currentTheme);
  }

  /**
   * Load theme preference from storage or system
   * @returns {string} Theme name
   */
  loadThemePreference() {
    try {
      // Check localStorage first
      const savedTheme = localStorage.getItem(this.storageKey);
      if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
        return savedTheme;
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
    }

    // Fall back to system preference
    return this.getSystemTheme();
  }

  /**
   * Get system theme preference
   * @returns {string} System theme
   */
  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  /**
   * Save theme preference to storage
   * @param {string} theme - Theme to save
   */
  saveThemePreference(theme) {
    try {
      localStorage.setItem(this.storageKey, theme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }

  /**
   * Apply theme to document
   * @param {string} theme - Theme to apply
   */
  applyTheme(theme) {
    const html = document.documentElement;
    
    // Remove existing theme classes
    html.classList.remove('theme-light', 'theme-dark');
    html.removeAttribute('data-theme');
    
    // Add new theme
    html.classList.add(`theme-${theme}`);
    html.setAttribute('data-theme', theme);
    
    // Update theme color meta tag
    this.updateThemeColorMeta(theme);
    
    this.currentTheme = theme;
    
    // Notify callbacks
    this.notifyThemeChange(theme);
  }

  /**
   * Update theme color meta tag
   * @param {string} theme - Current theme
   */
  updateThemeColorMeta(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    
    const colors = {
      light: '#2196F3',
      dark: '#1976D2'
    };
    
    metaThemeColor.content = colors[theme] || colors.light;
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  /**
   * Set specific theme
   * @param {string} theme - Theme to set
   */
  setTheme(theme) {
    if (!['light', 'dark'].includes(theme)) {
      console.warn('Invalid theme:', theme);
      return;
    }

    this.applyTheme(theme);
    this.saveThemePreference(theme);
    this.updateThemeToggleButton();
  }

  /**
   * Get current theme
   * @returns {string} Current theme
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * Setup theme toggle button
   */
  setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', this.toggleTheme);
      this.updateThemeToggleButton();
    }
  }

  /**
   * Update theme toggle button appearance
   */
  updateThemeToggleButton() {
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) return;

    const icon = themeToggle.querySelector('.theme-icon');
    if (!icon) return;

    // Update icon based on current theme
    if (this.currentTheme === 'dark') {
      // Show moon icon for dark theme
      icon.innerHTML = `
        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
      `;
      themeToggle.setAttribute('aria-label', 'Switch to light mode');
      themeToggle.setAttribute('title', 'Switch to light mode');
    } else {
      // Show sun icon for light theme
      icon.innerHTML = `
        <circle cx="12" cy="12" r="5"></circle>
        <line x1="12" y1="1" x2="12" y2="3"></line>
        <line x1="12" y1="21" x2="12" y2="23"></line>
        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
        <line x1="1" y1="12" x2="3" y2="12"></line>
        <line x1="21" y1="12" x2="23" y2="12"></line>
        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
      `;
      themeToggle.setAttribute('aria-label', 'Switch to dark mode');
      themeToggle.setAttribute('title', 'Switch to dark mode');
    }
  }

  /**
   * Setup system theme change listener
   */
  setupSystemThemeListener() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', this.handleSystemThemeChange);
    }
  }

  /**
   * Handle system theme change
   * @param {MediaQueryListEvent} e - Media query event
   */
  handleSystemThemeChange(e) {
    // Only auto-switch if user hasn't manually set a preference
    const hasManualPreference = localStorage.getItem(this.storageKey) !== null;
    if (!hasManualPreference) {
      const systemTheme = e.matches ? 'dark' : 'light';
      this.applyTheme(systemTheme);
      this.updateThemeToggleButton();
    }
  }

  /**
   * Register callback for theme changes
   * @param {Function} callback - Callback function
   */
  onThemeChange(callback) {
    if (typeof callback === 'function') {
      this.themeChangeCallbacks.push(callback);
    }
  }

  /**
   * Unregister theme change callback
   * @param {Function} callback - Callback function to remove
   */
  offThemeChange(callback) {
    const index = this.themeChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.themeChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * Notify all callbacks of theme change
   * @param {string} theme - New theme
   */
  notifyThemeChange(theme) {
    this.themeChangeCallbacks.forEach(callback => {
      try {
        callback(theme);
      } catch (error) {
        console.error('Error in theme change callback:', error);
      }
    });
  }

  /**
   * Get available themes
   * @returns {Array} Available theme names
   */
  getAvailableThemes() {
    return ['light', 'dark'];
  }

  /**
   * Check if theme is supported
   * @param {string} theme - Theme to check
   * @returns {boolean} Whether theme is supported
   */
  isThemeSupported(theme) {
    return this.getAvailableThemes().includes(theme);
  }

  /**
   * Get theme display name
   * @param {string} theme - Theme name
   * @returns {string} Display name
   */
  getThemeDisplayName(theme) {
    const displayNames = {
      light: 'Light',
      dark: 'Dark'
    };
    return displayNames[theme] || theme;
  }

  /**
   * Reset theme to system preference
   */
  resetToSystemTheme() {
    // Remove manual preference
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to remove theme preference:', error);
    }
    
    // Apply system theme
    const systemTheme = this.getSystemTheme();
    this.applyTheme(systemTheme);
    this.updateThemeToggleButton();
  }

  /**
   * Get theme CSS custom properties
   * @param {string} theme - Theme name
   * @returns {Object} CSS custom properties
   */
  getThemeProperties(theme) {
    // This could be expanded to return theme-specific CSS properties
    // for programmatic styling
    const properties = {
      light: {
        '--color-primary': '#2196F3',
        '--bg-primary': '#ffffff',
        '--text-primary': '#212529'
      },
      dark: {
        '--color-primary': '#2196F3',
        '--bg-primary': '#1a1a1a',
        '--text-primary': '#ffffff'
      }
    };
    
    return properties[theme] || properties.light;
  }

  /**
   * Apply custom theme properties
   * @param {Object} properties - CSS custom properties
   */
  applyCustomProperties(properties) {
    const root = document.documentElement;
    Object.entries(properties).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  /**
   * Cleanup theme manager
   */
  destroy() {
    // Remove event listeners
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
    }
    
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.removeEventListener('click', this.toggleTheme);
    }
    
    // Clear callbacks
    this.themeChangeCallbacks = [];
  }
}
