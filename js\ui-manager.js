/**
 * UI Manager
 * Handles all UI interactions and updates
 */

export class UIManager {
  constructor() {
    this.currentCode = '';
    this.toastContainer = document.getElementById('toast-container');
    this.loadingSpinner = document.getElementById('loading-spinner');
    this.infoBanner = document.getElementById('info-banner');
    this.infoMessage = document.getElementById('info-message');
    this.dartCodeElement = document.getElementById('dartCode');
    this.codePlaceholder = document.getElementById('code-placeholder');
    this.codeOutputContainer = document.querySelector('.code-output');
  }

  /**
   * Show loading spinner with message
   * @param {string} message - Loading message
   */
  showLoading(message = 'Loading...') {
    if (this.loadingSpinner) {
      const messageElement = this.loadingSpinner.querySelector('p');
      if (messageElement) {
        messageElement.textContent = message;
      }
      this.loadingSpinner.classList.add('show');
    }
  }

  /**
   * Hide loading spinner
   */
  hideLoading() {
    if (this.loadingSpinner) {
      this.loadingSpinner.classList.remove('show');
    }
  }

  /**
   * Show toast notification
   * @param {string} message - Toast message
   * @param {string} type - Toast type (success, error, warning, info)
   * @param {number} duration - Duration in milliseconds
   */
  showToast(message, type = 'info', duration = 4000) {
    if (!this.toastContainer) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = this.getToastIcon(type);
    
    toast.innerHTML = `
      ${icon}
      <span class="toast-message">${message}</span>
      <button class="toast-close" aria-label="Close notification">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    `;

    // Add close functionality
    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', () => {
      this.removeToast(toast);
    });

    // Add to container
    this.toastContainer.appendChild(toast);

    // Trigger animation
    requestAnimationFrame(() => {
      toast.classList.add('show');
    });

    // Auto remove after duration
    setTimeout(() => {
      this.removeToast(toast);
    }, duration);
  }

  /**
   * Get icon for toast type
   * @param {string} type - Toast type
   * @returns {string} SVG icon HTML
   */
  getToastIcon(type) {
    const icons = {
      success: `<svg class="toast-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="20,6 9,17 4,12"></polyline>
      </svg>`,
      error: `<svg class="toast-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="15" y1="9" x2="9" y2="15"></line>
        <line x1="9" y1="9" x2="15" y2="15"></line>
      </svg>`,
      warning: `<svg class="toast-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="m21.73,18l-8-14a2,2 0 0,0 -3.46,0l-8,14A2,2 0 0,0 4,21H20a2,2 0 0,0 1.73,-3Z"></path>
        <line x1="12" y1="9" x2="12" y2="13"></line>
        <line x1="12" y1="17" x2="12.01" y2="17"></line>
      </svg>`,
      info: `<svg class="toast-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="16" x2="12" y2="12"></line>
        <line x1="12" y1="8" x2="12.01" y2="8"></line>
      </svg>`
    };
    return icons[type] || icons.info;
  }

  /**
   * Remove toast notification
   * @param {HTMLElement} toast - Toast element to remove
   */
  removeToast(toast) {
    if (toast && toast.parentNode) {
      toast.classList.remove('show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 250);
    }
  }

  /**
   * Show info banner with message
   * @param {string} message - Info message
   */
  showInfoBanner(message) {
    if (this.infoBanner && this.infoMessage) {
      this.infoMessage.innerHTML = message;
      this.infoBanner.style.display = 'block';
    }
  }

  /**
   * Hide info banner
   */
  hideInfoBanner() {
    if (this.infoBanner) {
      this.infoBanner.style.display = 'none';
    }
  }

  /**
   * Show generated Dart code
   * @param {string} dartCode - Generated Dart code
   */
  showGeneratedCode(dartCode) {
    this.currentCode = dartCode;

    if (this.dartCodeElement && this.codePlaceholder && this.codeOutputContainer) {
      if (dartCode && dartCode.trim()) {
        // Show code
        this.dartCodeElement.textContent = dartCode;
        this.codeOutputContainer.classList.add('show');
        this.codePlaceholder.style.display = 'none';

        // Apply syntax highlighting
        this.applySyntaxHighlighting();
      } else {
        // Show placeholder
        this.codeOutputContainer.classList.remove('show');
        this.codePlaceholder.style.display = 'block';
      }
    }
  }

  /**
   * Show code error
   * @param {string} errorMessage - Error message
   */
  showCodeError(errorMessage) {
    if (this.dartCodeElement && this.codePlaceholder && this.codeOutputContainer) {
      this.dartCodeElement.textContent = `// Error: ${errorMessage}`;
      this.codeOutputContainer.classList.add('show');
      this.codePlaceholder.style.display = 'none';

      // Show error in info banner
      this.showInfoBanner(`<strong>Error:</strong> ${errorMessage}`);
    }
  }

  /**
   * Apply syntax highlighting to code
   */
  applySyntaxHighlighting() {
    if (this.dartCodeElement && window.hljs) {
      try {
        // Remove existing highlighting
        this.dartCodeElement.removeAttribute('data-highlighted');
        
        // Apply new highlighting
        window.hljs.highlightElement(this.dartCodeElement);
      } catch (error) {
        console.warn('Failed to apply syntax highlighting:', error);
      }
    }
  }

  /**
   * Get current generated code
   * @returns {string} Current Dart code
   */
  getCurrentCode() {
    return this.currentCode;
  }

  /**
   * Update button state
   * @param {string} buttonId - Button element ID
   * @param {boolean} disabled - Whether button should be disabled
   * @param {string} text - Button text
   */
  updateButtonState(buttonId, disabled = false, text = null) {
    const button = document.getElementById(buttonId);
    if (button) {
      button.disabled = disabled;
      if (text) {
        const textElement = button.querySelector('span');
        if (textElement) {
          textElement.textContent = text;
        } else {
          // If no span, update button text directly (but preserve icons)
          const svg = button.querySelector('svg');
          button.innerHTML = '';
          if (svg) {
            button.appendChild(svg);
          }
          button.appendChild(document.createTextNode(text));
        }
      }
    }
  }

  /**
   * Show/hide element
   * @param {string} elementId - Element ID
   * @param {boolean} show - Whether to show the element
   */
  toggleElement(elementId, show) {
    const element = document.getElementById(elementId);
    if (element) {
      element.style.display = show ? 'block' : 'none';
    }
  }

  /**
   * Add loading state to button
   * @param {string} buttonId - Button element ID
   * @param {boolean} loading - Whether button is in loading state
   */
  setButtonLoading(buttonId, loading) {
    const button = document.getElementById(buttonId);
    if (button) {
      if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        
        // Add spinner if not exists
        let spinner = button.querySelector('.button-spinner');
        if (!spinner) {
          spinner = document.createElement('div');
          spinner.className = 'button-spinner';
          spinner.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
          `;
          button.insertBefore(spinner, button.firstChild);
        }
      } else {
        button.disabled = false;
        button.classList.remove('loading');
        
        // Remove spinner
        const spinner = button.querySelector('.button-spinner');
        if (spinner) {
          spinner.remove();
        }
      }
    }
  }

  /**
   * Animate element
   * @param {string} elementId - Element ID
   * @param {string} animation - Animation class name
   * @param {number} duration - Animation duration in milliseconds
   */
  animateElement(elementId, animation, duration = 300) {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.add(animation);
      setTimeout(() => {
        element.classList.remove(animation);
      }, duration);
    }
  }

  /**
   * Update progress indicator
   * @param {number} progress - Progress percentage (0-100)
   */
  updateProgress(progress) {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
    }
  }

  /**
   * Show confirmation dialog
   * @param {string} message - Confirmation message
   * @param {string} confirmText - Confirm button text
   * @param {string} cancelText - Cancel button text
   * @returns {Promise<boolean>} User's choice
   */
  showConfirmDialog(message, confirmText = 'Confirm', cancelText = 'Cancel') {
    return new Promise((resolve) => {
      // For now, use native confirm dialog
      // In a more advanced implementation, you could create a custom modal
      const result = confirm(message);
      resolve(result);
    });
  }

  /**
   * Scroll element into view smoothly
   * @param {string} elementId - Element ID
   */
  scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }

  /**
   * Focus element
   * @param {string} elementId - Element ID
   */
  focusElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
    }
  }

  /**
   * Get form data from configuration form
   * @returns {Object} Form data
   */
  getFormData() {
    const formData = {};
    const form = document.querySelector('.config-section');
    
    if (form) {
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        if (input.type === 'checkbox') {
          formData[input.id] = input.checked;
        } else {
          formData[input.id] = input.value;
        }
      });
    }
    
    return formData;
  }

  /**
   * Set form data to configuration form
   * @param {Object} data - Form data
   */
  setFormData(data) {
    Object.entries(data).forEach(([key, value]) => {
      const input = document.getElementById(key);
      if (input) {
        if (input.type === 'checkbox') {
          input.checked = value;
        } else {
          input.value = value;
        }
      }
    });
  }
}
