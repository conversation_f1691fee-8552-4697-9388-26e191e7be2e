{"name": "JSON to Dart Model Generator", "short_name": "JSON to Dart", "description": "Convert JSON data to Dart model classes with null safety, serialization methods, and customizable options", "version": "1.0.0", "start_url": "/", "display": "standalone", "orientation": "any", "theme_color": "#2196F3", "background_color": "#ffffff", "scope": "/", "lang": "en", "dir": "ltr", "icons": [{"src": "favicon.ico", "sizes": "16x16 32x32 48x48", "type": "image/x-icon", "purpose": "any"}, {"src": "icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "categories": ["developer", "productivity", "utilities"], "screenshots": [{"src": "screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "JSON to Dart converter on desktop"}, {"src": "screenshots/mobile-1.png", "sizes": "390x844", "type": "image/png", "platform": "narrow", "label": "JSON to Dart converter on mobile"}], "shortcuts": [{"name": "New Conversion", "short_name": "New", "description": "Start a new JSON to Dart conversion", "url": "/?action=new", "icons": [{"src": "icons/shortcut-new.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Examples", "short_name": "Examples", "description": "View example JSON structures", "url": "/?action=examples", "icons": [{"src": "icons/shortcut-examples.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [{"platform": "web", "url": "https://ashamp.github.io/jsonToDartModel/"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "focus-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate", "file_handlers": [{"action": "/", "accept": {"application/json": [".json"], "text/plain": [".txt"]}, "launch_type": "single-client"}], "protocol_handlers": [{"protocol": "web+jsontodart", "url": "/?json=%s"}], "share_target": {"action": "/", "method": "GET", "params": {"title": "title", "text": "text", "url": "url"}}}