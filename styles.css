/* CSS Custom Properties (Variables) */
:root {
  /* Colors - Light Theme */
  --color-primary: #2196F3;
  --color-primary-dark: #1976D2;
  --color-primary-light: #BBDEFB;
  --color-secondary: #FF9800;
  --color-success: #4CAF50;
  --color-warning: #FF9800;
  --color-error: #F44336;
  --color-info: #2196F3;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-color: #dee2e6;
  --border-color-light: #e9ecef;
  --border-color-dark: #adb5bd;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --bg-overlay: rgba(0, 0, 0, 0.8);

  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;
  --text-inverse: #000000;

  --border-color: #404040;
  --border-color-light: #333333;
  --border-color-dark: #555555;

  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.08);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Focus Management */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Loading Spinner */
.loading-spinner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.loading-spinner.show {
  opacity: 1;
  visibility: visible;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-primary-light);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

.loading-spinner p {
  color: var(--text-inverse);
  font-weight: var(--font-weight-medium);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
}

.toast {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left: 4px solid var(--color-success);
}

.toast.error {
  border-left: 4px solid var(--color-error);
}

.toast.warning {
  border-left: 4px solid var(--color-warning);
}

.toast.info {
  border-left: 4px solid var(--color-info);
}

.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

.toast-close:hover {
  color: var(--text-primary);
}

/* App Container */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
}

/* Header */
.app-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .app-header {
  background-color: rgba(26, 26, 26, 0.95);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.header-title h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.header-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}
/* Buttons */
.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-button:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.github-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.github-link:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.primary-button,
.secondary-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  white-space: nowrap;
}

.primary-button {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.primary-button:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.secondary-button {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.secondary-button:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color-dark);
}

/* Info Banner */
.info-banner {
  background-color: var(--color-warning);
  color: var(--text-inverse);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin: var(--spacing-lg) auto 0;
  max-width: 1400px;
  margin-left: var(--spacing-lg);
  margin-right: var(--spacing-lg);
}

.info-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.info-icon {
  flex-shrink: 0;
}

.info-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  margin-left: auto;
  transition: opacity var(--transition-fast);
}

.info-close:hover {
  opacity: 0.8;
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
  width: 100%;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  height: 100%;
  min-height: calc(100vh - 200px);
}

/* Section Styles */
.json-input-section,
.dart-output-section {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.config-section {
  grid-column: 1 / -1;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.section-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* JSON Editor */
.json-editor-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}

.json-editor {
  height: 100%;
  border: none;
  font-family: var(--font-family-mono);
}

/* Configuration Grid */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.config-group {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
}

.config-group-full {
  grid-column: 1 / -1;
}

.config-group-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

.checkbox-label:hover {
  color: var(--color-primary);
}

.checkbox-sub {
  margin-left: var(--spacing-lg);
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color-dark);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-text {
  user-select: none;
}
/* Input Groups */
.input-group {
  margin-bottom: var(--spacing-md);
}

.input-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.text-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-family: var(--font-family-primary);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: all var(--transition-fast);
}

.text-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.text-input:read-only {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.text-input::placeholder {
  color: var(--text-muted);
}

/* Code Output */
.code-output-container {
  flex: 1;
  position: relative;
  background-color: var(--bg-secondary);
}

.code-output {
  height: 100%;
  margin: 0;
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border: none;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  display: none; /* Initially hidden */
}

.code-output.show {
  display: block !important;
}

.code-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-muted);
  pointer-events: none;
  display: block;
  z-index: 1;
}

.code-placeholder svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.code-placeholder h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
}

.code-placeholder p {
  font-size: var(--font-size-sm);
  max-width: 300px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 var(--spacing-md);
  }

  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .config-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .header-title h1 {
    font-size: var(--font-size-2xl);
  }

  .header-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  .github-link span {
    display: none;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    min-height: auto;
  }

  .config-section {
    order: -1;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .json-editor-container {
    min-height: 300px;
  }

  .toast-container {
    left: var(--spacing-md);
    right: var(--spacing-md);
    top: var(--spacing-md);
    max-width: none;
  }

  .info-banner {
    margin-left: var(--spacing-md);
    margin-right: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-sm);
  }

  .main-content {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .section-header {
    padding: var(--spacing-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .section-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  .config-section {
    padding: var(--spacing-md);
  }

  .config-group {
    padding: var(--spacing-md);
  }

  .primary-button,
  .secondary-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .primary-button span,
  .secondary-button span {
    display: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --border-color-light: #333333;
    --border-color-dark: #000000;
  }

  [data-theme="dark"] {
    --border-color: #ffffff;
    --border-color-light: #cccccc;
    --border-color-dark: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .loading-spinner .spinner {
    animation: none;
  }
}

/* Print styles */
@media print {
  .app-header,
  .config-section,
  .section-header,
  .loading-spinner,
  .toast-container,
  .info-banner {
    display: none !important;
  }

  .main-content {
    padding: 0;
    max-width: none;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .json-input-section,
  .dart-output-section {
    border: 1px solid #000000;
    box-shadow: none;
    page-break-inside: avoid;
  }

  .code-output {
    background-color: transparent;
    font-size: 10pt;
    line-height: 1.4;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-color-dark);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color-dark) var(--bg-secondary);
}
/* Button Loading States */
.primary-button.loading,
.secondary-button.loading {
  position: relative;
  color: transparent;
}

.button-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
}

.button-spinner svg {
  animation: spin 1s linear infinite;
  color: currentColor;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.pulse {
  animation: pulse 1s ease-in-out infinite;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* Enhanced Focus States */
.text-input:focus,
.checkbox-label:focus-within,
.primary-button:focus,
.secondary-button:focus,
.icon-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Improved Hover Effects */
.json-input-section:hover,
.dart-output-section:hover,
.config-group:hover {
  box-shadow: var(--shadow-md);
  transition: box-shadow var(--transition-normal);
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  border-radius: var(--radius-sm);
}

.skeleton-text:last-child {
  margin-bottom: 0;
  width: 60%;
}

/* Progress Bar */
.progress-container {
  width: 100%;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin: var(--spacing-md) 0;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
  width: 0%;
}

/* Enhanced JSON Editor Styles */
.json-editor-container .jsoneditor {
  border: none !important;
  border-radius: 0 !important;
}

.json-editor-container .jsoneditor-menu {
  background-color: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.json-editor-container .ace_editor {
  font-family: var(--font-family-mono) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Code Syntax Highlighting Overrides */
.code-output code {
  background: transparent !important;
}

/* Dark theme syntax highlighting */
[data-theme="dark"] .code-output {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .hljs {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Improved Responsive Behavior */
@media (max-width: 640px) {
  .header-title h1 {
    font-size: var(--font-size-xl);
  }

  .header-subtitle {
    font-size: var(--font-size-sm);
  }

  .config-group-title {
    font-size: var(--font-size-base);
  }

  .checkbox-text {
    font-size: var(--font-size-xs);
  }

  .section-header h2 {
    font-size: var(--font-size-lg);
  }
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.p-sm {
  padding: var(--spacing-sm);
}

.p-md {
  padding: var(--spacing-md);
}

.p-lg {
  padding: var(--spacing-lg);
}

/* Enhanced Error States */
.text-input.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.checkbox-label.error .checkbox-custom {
  border-color: var(--color-error);
}

.error-message {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.error-message svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* Success States */
.text-input.success {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.success-message {
  color: var(--color-success);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* Improved Accessibility */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .slide-in-right,
  .pulse,
  .shake {
    animation: none;
  }

  .skeleton {
    animation: none;
    background: var(--bg-tertiary);
  }
}

/* High contrast improvements */
@media (prefers-contrast: high) {
  .primary-button {
    border: 2px solid currentColor;
  }

  .secondary-button {
    border: 2px solid currentColor;
  }

  .checkbox-custom {
    border-width: 3px;
  }

  .text-input {
    border-width: 2px;
  }
}
/* Mobile-First Responsive Enhancements */

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .primary-button,
  .secondary-button,
  .icon-button {
    min-height: 44px;
    min-width: 44px;
    padding: var(--spacing-md);
  }

  .checkbox-custom {
    width: 24px;
    height: 24px;
  }

  .text-input {
    min-height: 44px;
    padding: var(--spacing-md);
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .toast {
    padding: var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* Landscape phone optimization */
@media (max-width: 896px) and (orientation: landscape) {
  .app-header {
    padding: var(--spacing-sm) 0;
  }

  .header-title h1 {
    font-size: var(--font-size-lg);
    margin-bottom: 0;
  }

  .header-subtitle {
    display: none;
  }

  .main-content {
    padding: var(--spacing-md);
  }

  .content-grid {
    gap: var(--spacing-md);
  }

  .config-section {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .json-editor-container {
    min-height: 250px;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .config-section {
    order: -1;
  }

  .config-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .config-group-full {
    grid-column: 1 / -1;
  }

  .json-editor-container {
    min-height: 350px;
  }
}

/* Large tablet and small desktop */
@media (min-width: 1024px) and (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
  }

  .config-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Ultra-wide screen optimization */
@media (min-width: 1600px) {
  .main-content {
    max-width: 1600px;
  }

  .content-grid {
    gap: var(--spacing-2xl);
  }

  .config-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Improved mobile navigation */
@media (max-width: 768px) {
  .section-header {
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    background-color: var(--bg-secondary);
    backdrop-filter: blur(8px);
  }

  .json-input-section,
  .dart-output-section {
    border-radius: var(--radius-md);
  }

  .config-section {
    border-radius: var(--radius-md);
  }
}

/* Improved focus management for mobile */
@media (max-width: 768px) {
  .text-input:focus {
    transform: scale(1.02);
    transition: transform var(--transition-fast);
  }

  .checkbox-label:focus-within {
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    padding: var(--spacing-xs);
    margin: calc(-1 * var(--spacing-xs));
  }
}

/* Print optimizations */
@media print {
  .content-grid {
    display: block;
  }

  .json-input-section {
    margin-bottom: var(--spacing-lg);
    page-break-after: avoid;
  }

  .dart-output-section {
    page-break-before: avoid;
  }

  .config-section {
    display: none;
  }

  .code-output {
    font-size: 8pt;
    line-height: 1.2;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

/* Reduced data mode */
@media (prefers-reduced-data: reduce) {
  .skeleton {
    animation: none;
    background: var(--bg-tertiary);
  }

  .loading-spinner .spinner {
    animation-duration: 2s;
  }

  .fade-in,
  .slide-in-right {
    animation-duration: 0.1s;
  }
}

/* High refresh rate displays */
@media (min-resolution: 120dpi) {
  .primary-button,
  .secondary-button {
    transition-duration: 100ms;
  }

  .checkbox-custom {
    transition-duration: 100ms;
  }

  .text-input {
    transition-duration: 100ms;
  }
}

/* Improved container queries support (future-proofing) */
.json-input-section {
  container-type: inline-size;
}

.dart-output-section {
  container-type: inline-size;
}

/* Safe area insets for mobile devices with notches */
@supports (padding: max(0px)) {
  .app-header {
    padding-left: max(var(--spacing-lg), env(safe-area-inset-left));
    padding-right: max(var(--spacing-lg), env(safe-area-inset-right));
  }

  .main-content {
    padding-left: max(var(--spacing-lg), env(safe-area-inset-left));
    padding-right: max(var(--spacing-lg), env(safe-area-inset-right));
    padding-bottom: max(var(--spacing-xl), env(safe-area-inset-bottom));
  }

  .toast-container {
    top: max(var(--spacing-lg), env(safe-area-inset-top));
    right: max(var(--spacing-lg), env(safe-area-inset-right));
  }
}

/* Improved scrolling behavior */
.json-editor-container,
.code-output-container {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* Enhanced mobile keyboard handling */
@media (max-width: 768px) {
  .text-input:focus {
    scroll-margin-top: 100px;
  }

  /* Prevent viewport zoom when focusing inputs */
  .text-input {
    font-size: max(16px, var(--font-size-sm));
  }
}

/* Improved touch targets */
.checkbox-label {
  min-height: 44px;
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.icon-button {
  min-width: 44px;
  min-height: 44px;
}

/* Better visual hierarchy on small screens */
@media (max-width: 480px) {
  .config-group {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
  }

  .config-group:last-child {
    margin-bottom: 0;
  }

  .checkbox-group {
    gap: var(--spacing-sm);
  }

  .input-group {
    margin-bottom: var(--spacing-sm);
  }
}

/* Improved loading states for mobile */
@media (max-width: 768px) {
  .loading-spinner {
    backdrop-filter: blur(4px);
  }

  .loading-spinner p {
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-md);
  }

  .spinner {
    width: 48px;
    height: 48px;
    border-width: 4px;
  }
}
/* PWA Install Banner */
.pwa-install-banner {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  max-width: 500px;
  margin: 0 auto;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .pwa-install-banner {
  background-color: rgba(26, 26, 26, 0.95);
}

.pwa-install-content {
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.pwa-install-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.pwa-install-icon {
  color: var(--color-primary);
  flex-shrink: 0;
}

.pwa-install-text h3 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.pwa-install-text p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.pwa-install-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

/* Mobile PWA Install Banner */
@media (max-width: 768px) {
  .pwa-install-banner {
    left: var(--spacing-md);
    right: var(--spacing-md);
    bottom: var(--spacing-md);
  }

  .pwa-install-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .pwa-install-info {
    justify-content: center;
    text-align: center;
  }

  .pwa-install-actions {
    justify-content: center;
  }
}

/* Update Notification Styles */
.update-notification {
  background-color: var(--color-info);
  color: var(--text-inverse);
  border-left-color: var(--color-primary);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--color-warning);
  color: var(--text-inverse);
  padding: var(--spacing-sm);
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  z-index: var(--z-modal);
  transform: translateY(-100%);
  transition: transform var(--transition-normal);
}

.offline-indicator.show {
  transform: translateY(0);
}

/* Enhanced App Icon Styles */
.app-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  box-shadow: var(--shadow-md);
}

/* Splash Screen Styles */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  color: white;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.splash-screen.show {
  opacity: 1;
  visibility: visible;
}

.splash-screen .app-icon {
  margin-bottom: var(--spacing-xl);
  width: 80px;
  height: 80px;
  font-size: var(--font-size-2xl);
}

.splash-screen h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.splash-screen p {
  font-size: var(--font-size-base);
  opacity: 0.9;
  text-align: center;
  max-width: 300px;
}

/* File Drop Zone */
.file-drop-zone {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.file-drop-zone.show {
  opacity: 1;
  visibility: visible;
}

.file-drop-content {
  background-color: var(--bg-primary);
  border: 2px dashed var(--color-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  max-width: 400px;
  margin: var(--spacing-lg);
}

.file-drop-content svg {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.file-drop-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.file-drop-content p {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

/* Enhanced Accessibility for PWA */
@media (prefers-reduced-motion: reduce) {
  .pwa-install-banner {
    animation: none;
  }

  .splash-screen {
    transition: none;
  }

  .file-drop-zone {
    transition: none;
  }
}

/* High contrast mode for PWA elements */
@media (prefers-contrast: high) {
  .pwa-install-banner {
    border: 3px solid var(--text-primary);
  }

  .pwa-install-icon {
    color: var(--text-primary);
  }

  .file-drop-content {
    border-width: 4px;
  }
}